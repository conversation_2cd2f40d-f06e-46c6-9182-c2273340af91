'use client';
import { Mouse<PERSON><PERSON>Hand<PERSON>, useState, type FC, type ReactElement } from 'react';
import { useRouter } from 'next/navigation';
import Breadcrumbs from '@/components/common/breadcrumbs';
import { type BreadCrumbItem } from '@/types/common';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import ArrowButton from '@/components/common/arrow-button';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import GalleryThree from '@/components/common/gallery';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useGetAmenitiesTabs, useGetPropertyDetailTabs } from '@/hooks/use-get-amenities-tabs';
import TabListTriggers from '@/components/common/tab-list-triggers';
import PropertyDetailsActions from '@/components/property-details/property-details-actions';
import PropertyDetailsSummary from '@/components/property-details/property-details-summary';
import PropertyDetailsTab from '@/components/property-details/property-details-tab';
import PropertyDetailsBookNow from '@/components/property-details/property-details-book-now';
import Location from '@/components/property-details/location-details';
import PropertyDetailsPicturesTab from '@/components/property-details/property-details-pictures-tab';
import PropertyDetailsRatingsTab from '@/components/property-details/property-details-ratings-tab';
import DirectionArrow from '@/components/common/direction-arrow';
import Carousel from '@/components/ui/carousel';
import { useAddWishList } from '@/queries/wisthlist';
import { toast } from 'sonner';
import { getMessagesFromResponse, getPriceDetails } from '@/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import { keys } from '@/lib/react-query/keys';
import Link from 'next/link';
import useGetReservationPrice from '@/hooks/use-get-reservation-price';
import useGetCheckinCheckout from '@/hooks/use-get-checkin-checkout';
import styles from './styles.module.css';
import { ClassificationDetails } from '@/types/homepage';
import PropertyDetailsCommunicateHost from './property-details-communicate-host';
import { useUserStore } from '@/store/useUserStore';

type PropertyDetailsProps = {
  propertyId: string;
  property: ClassificationDetails;
  isNavFixed?: boolean;
};

const PropertyDetails: FC<PropertyDetailsProps> = ({ property, propertyId, isNavFixed = false }): ReactElement => {
  const ct = useScopedI18n('common');
  const t = useScopedI18n('property_details');
  const wt = useScopedI18n('wishlist');
  const hlt = useScopedI18n('homepage.location');
  const [date] = useGetCheckinCheckout(property);
  const router = useRouter();
  const amenities = useGetAmenitiesTabs(property);
  const propertyTabs = useGetPropertyDetailTabs();
  const wishlistApi = useAddWishList();
  const [isFav, setIsFav] = useState(property?.is_wishlist || false);
  const queryClient = useQueryClient();
  const [reservation] = useGetReservationPrice(propertyId, date, 1, 1);
  const [perNight] = getPriceDetails(reservation?.price);
  const { userData, setAuthModalOpen } = useUserStore();

  const items: BreadCrumbItem[] = [
    { href: '/', label: ct('home') },
    { label: property?.residential_tourist },
    { label: property?.address?.city },
    { label: property?.name },
  ];

  const handleBackHomeClick = (): void => {
    router.push(`/`);
  };

  const handleAddToFavClick: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.preventDefault();
    if (!userData) {
      setAuthModalOpen(true);
    } else {
      handleWishlistToggle(property?.id);
    }
  };
  const handleWishlistToggle = (propertyId?: number) => {
    if (!propertyId) return;
    toast.dismiss();
    wishlistApi.mutate(
      {
        room_classification_id: propertyId,
      },
      {
        onSuccess: () => {
          if (isFav) {
            setIsFav(false);
            toast.success(wt('remove_from_wishlist'));
          } else {
            setIsFav(true);
            toast.success(wt('add_to_wishlist'));
          }
          queryClient.invalidateQueries({ queryKey: keys.propertyDetails });
        },
        onError: (e) => {
          const errors = getMessagesFromResponse(e);
          if (!Array.isArray(errors)) toast.error(errors);
        },
      }
    );
  };

  const handleGoToChat: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.preventDefault();
    if (!userData) {
      setAuthModalOpen(true);
    } else {
      router.push(`/chat?id=${propertyId}&chat_id=${property?.main_id}`);
    }
  };

  return (
    <div className="md:mt-8 md:flex md:flex-col md:gap-y-8">
      <div className="container-layout">
        <div className="hidden flex-col-reverse items-stretch justify-between gap-y-4 md:flex md:flex-row md:items-center">
          <Breadcrumbs items={items} />
          <ArrowButton label={ct('back_home')} onClick={handleBackHomeClick} />
        </div>
      </div>

      <div className="md:flex md:flex-col md:gap-y-6">
        <div className="md:flex md:flex-col md:gap-5">
          <div
            className={`${isNavFixed ? 'bg-white-full drop-shadow-custom' : ''} fixed left-0 top-0 z-40 w-full py-4 transition-all duration-300 ease-in-out md:static md:py-0`}>
            <div className="container-layout">
              <div className=" flex w-full items-center justify-between md:relative md:top-0 md:z-0 md:px-0 ">
                <p className="text-secondary headline-4 hidden md:block">
                  {property.name}
                  {Boolean(property.classification_name) && ` - ${property.classification_name}`}
                </p>
                <Button
                  variant="outline"
                  size="icon_sm"
                  className={`${styles.navBtn} bg-white-full border-transparent text-white md:hidden md:bg-black/30`}
                  onClick={handleBackHomeClick}>
                  <DirectionArrow className="stroke-red" flip size={18} />
                </Button>
                <PropertyDetailsActions
                  details={property}
                  isWishlist={property?.is_wishlist || isFav}
                  handleWishlistToggle={handleAddToFavClick}
                />
              </div>
            </div>
          </div>

          <div className={`${styles.carouselWrapper} relative md:hidden`}>
            <Carousel
              slideClassName=""
              withZoom={false}
              imgClassName="h-[300px] mobile:h-[360px] w-full"
              images={property.photos}
            />
          </div>
        </div>

        <div className="container-layout hidden md:block ">
          <div className="mb-6 hidden items-center justify-between md:flex">
            <PropertyDetailsSummary item={property} />
            <div className="items-center gap-3 ">
              <Button onClick={handleGoToChat} variant="ghost" className="gap-2 px-0">
                <IconWrapper name="Message" className="text-gray-400" />
                <p className="lg-text text-gray-400">{ct('ask_host')}</p>
              </Button>
            </div>
          </div>
          <div className="hidden md:block">
            <GalleryThree images={property.photos} />
          </div>
        </div>
        <div className="bg-white-full relative -mt-20 overflow-hidden rounded-t-xl md:mt-0 md:py-10">
          <div className="container-layout">
            <div className="bg-white-50 z-20  mb-28 rounded-t-2xl md:mb-0 md:mt-0 md:rounded-t-none md:bg-transparent md:px-0">
              <div className="lg:flex lg:flex-row lg:items-start lg:gap-14">
                <div className="mb-4 mt-4 lg:mb-0 lg:mt-0 lg:basis-2/3">
                  <Tabs defaultValue="detail">
                    <TabListTriggers items={propertyTabs} className="basis-1/4 data-[state=active]:basis-1/3" />
                    <TabsContent className="bg-transparent" value="detail">
                      <PropertyDetailsTab handleGoToChat={handleGoToChat} property={property} amenities={amenities} />
                    </TabsContent>
                    <TabsContent className="bg-transparent" value="location">
                      <div className="relative grid h-[43vh] w-full  xl:grid-cols-1 ">
                        <Location
                          properties={{
                            ...(property ?? {}),
                            night_price: {
                              ...(property?.night_price ?? {}),
                              per_night_price: perNight as number,
                              pre_night_price: perNight as number,
                            },
                          }}
                        />
                      </div>
                      <div className="mt-5  w-full items-center justify-between lg:flex">
                        <div className="gap-5">
                          <p className="lg-text text-secondary font-bold">{`${hlt('location')} ${property?.address?.city},${property.t_direction}`}</p>
                          <p className="lg-text mt-1 text-gray-400">{hlt('title')}</p>
                        </div>
                        <div className="flex justify-end">
                          <Link
                            href={`https://www.google.com/maps?q=${property.address.city_latLong.latitude},${property.address.city_latLong.longitude}`}
                            target="_blank">
                            <Button
                              variant="outline-secondary"
                              className="text-secondary sm-text mt-2 h-auto gap-2 rounded-full border-none p-4 hover:bg-transparent hover:shadow-none lg:mt-0">
                              <p className="lg-text text-gray-400">{hlt('link')}</p>
                              <IconWrapper name="Routing" size={30} className="bg-primary rounded p-1 text-white" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent className="bg-transparent" value="pictures">
                      <PropertyDetailsPicturesTab classificationId={propertyId} />
                    </TabsContent>
                    <TabsContent className="bg-transparent" value="reviews">
                      <PropertyDetailsRatingsTab propertyId={propertyId} />
                    </TabsContent>
                  </Tabs>
                </div>
                <PropertyDetailsBookNow
                  header={<p className="text-secondary sub-headline-sm mb-2">{t('reservation_details')}</p>}
                  property={property}
                  propertyId={propertyId}
                />
                <PropertyDetailsCommunicateHost
                  handleGoToChat={handleGoToChat}
                  showTitle
                  className="mt-6  flex flex-col gap-5 md:hidden"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PropertyDetails;
