.wrapper > div {
  @apply grid auto-rows-[150px] grid-cols-2 gap-2 md:gap-4;
}

.navBtn path {
  @apply stroke-secondary md:stroke-gray-500;
}

.carouselWrapper {
  padding-bottom: 60px;
}
.carouselWrapper :global(.splide__pagination) {
  bottom: 35px;
  justify-content: flex-start;
}
.likeBtn path {
  @apply stroke-primary;
}
.likeBtn svg {
  @apply fill-primary;
}

.bookBtn {
  @apply h-16 grow  rounded-none;
  @apply rounded-se-6xl md:rounded-es-lg;
}
.shareBtnsWrap {
  @apply flex items-center justify-between gap-3;
}
.shareBtnsWrap :global(.react-share__ShareButton) {
  @apply h-12 w-12 overflow-hidden rounded-full;
}
.shareBtnsWrap :global(.react-share__ShareButton) svg {
  @apply h-full w-full;
}
.shareBtnsWrap :global(.react-share__ShareButton) svg path {
  transform-origin: center;
  transform: scale(0.93);
}
