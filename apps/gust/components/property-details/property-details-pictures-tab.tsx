import { CSSProperties, type FC, type ReactElement } from 'react';
import LightGallery from 'lightgallery/react';
import 'lightgallery/css/lightgallery.css';
import 'lightgallery/css/lg-thumbnail.css';
import 'lightgallery/css/lg-zoom.css';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgZoom from 'lightgallery/plugins/zoom';
import ZoomImage from '../ui/zoom-image';
import { Photos } from '@/types/homepage';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import styles from './styles.module.css';
import { useGetPropertyPhotos } from '@/queries/property-details';
import { Skeleton } from '../ui/skeleton';

type PropertyDetailsPicturesTabProps = {
  classificationId: string;
};

const rowSpansPattern = [3, 2, 1, 1, 2, 1];

const gridRowsMobileSizes = ['152px', '55px', '97px', '15px', '64px', '121px', '72px'];
const gridRowsDesktopSizes = ['355px', '130px', '225px', '36px', '148px', '283px', '166px'];

const PropertyDetailsPicturesTab: FC<PropertyDetailsPicturesTabProps> = ({ classificationId }): ReactElement => {
  const t = useScopedI18n('property_details');
  const { data, isLoading } = useGetPropertyPhotos({ classificationId });
  const photosList = data?.data?.data;

  if (isLoading) return <GridSkeleton />;

  return (
    <>
      <div>
        <div
          className={styles.wrapper}
          style={
            {
              '--grid-rows-mobile': `${gridRowsMobileSizes.slice(0, (photosList ?? [])?.length - 1).join(' ')}`,
              '--grid-rows-desktop': `${gridRowsDesktopSizes.slice(0, (photosList ?? [])?.length - 1).join(' ')}`,
            } as CSSProperties
          }>
          <LightGallery elementClassNames="gallery-item" plugins={[lgZoom, lgThumbnail]} speed={500}>
            {photosList?.map((photo, index) => {
              const span = rowSpansPattern[index % rowSpansPattern.length];
              const imgSrc = photo?.original_image;
              return (
                <a
                  key={index}
                  href={imgSrc}
                  className={`relative block w-full overflow-hidden rounded-lg row-span-${span}`}>
                  <img src={imgSrc} alt={`Photo ${index + 1}`} className="h-full w-full object-cover" />
                  <ZoomImage alt={`${t('property_image')} ${index + 1}`} image={imgSrc} withZoom={false} />
                </a>
              );
            })}
          </LightGallery>
        </div>
      </div>
    </>
  );
};
export default PropertyDetailsPicturesTab;

const GridSkeleton: FC = () => (
  <div>
    <div
      className={styles.wrapper}
      style={
        {
          '--grid-rows-mobile': `${gridRowsMobileSizes.slice(0, 6).join(' ')}`,
          '--grid-rows-desktop': `${gridRowsDesktopSizes.slice(0, 6).join(' ')}`,
        } as CSSProperties
      }>
      <LightGallery elementClassNames="gallery-item" plugins={[lgZoom, lgThumbnail]} speed={500}>
        {Array.from({ length: 6 }).map((_, index) => {
          const span = rowSpansPattern[index % rowSpansPattern.length];
          return (
            <div key={index} className={`relative block w-full overflow-hidden rounded-lg row-span-${span}`}>
              <Skeleton className="h-full w-full bg-gray-200" />
            </div>
          );
        })}
      </LightGallery>
    </div>
  </div>
);
