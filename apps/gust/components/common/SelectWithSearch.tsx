'use client';

import { useEffect, useRef, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';

interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

interface CustomSelectProps {
  options: Option[];
  value?: string;
  defaultValue?: string;
  onChange?: (val: string) => void;
  placeholder?: string;
  className?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  dir?: 'rtl' | 'ltr';
  isDisabled?: boolean;
  isLoading?: boolean;
  classNameDropdown?: string;
  asyncFetch?: () => Promise<Option[]>;
}

export default function SelectWithSearch({
  options: initialOptions = [],
  value,
  defaultValue = '',
  onChange,
  placeholder = '',
  className,
  searchable = true,
  searchPlaceholder = '',
  dir = 'rtl',
  isDisabled = false,
  isLoading = false,
  classNameDropdown,
  asyncFetch,
}: CustomSelectProps) {
  /* -------------------- state -------------------- */

  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [internalValue, setInternalValue] = useState<string>(defaultValue);
  const [options, setOptions] = useState<Option[]>(initialOptions);
  const [loading, setLoading] = useState(isLoading);
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const t = useScopedI18n('common');
  const locale = useCurrentLocale(); // ✅ تحديد اللغة الحالية

  const direction = locale === 'en' ? 'ltr' : 'rtl'; // ✅ تحديد الاتجاه حسب اللغة

  const selectedValue = value ?? internalValue;

  /* -------------------- refs -------------------- */
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLUListElement>(null);

  /* -------------------- side-effects -------------------- */
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (asyncFetch) {
      setLoading(true);
      asyncFetch().then((data) => {
        setOptions(data);
        setLoading(false);
      });
    }
  }, [asyncFetch]);

  useEffect(() => {
    if (value !== undefined && value !== internalValue) {
      setInternalValue(value);
    }
  }, [value, internalValue]);

  /* -------------------- helpers -------------------- */
  const filteredOptions = options.filter((o) => o.label.toLowerCase().includes(searchTerm.toLowerCase()));

  const handleSelect = (val: string) => {
    onChange?.(val);
    setInternalValue(val);
    setIsOpen(false);
    setSearchTerm('');
  };

  const renderSelected = () => {
    if (!selectedValue) return <span className="text-gray-100">{placeholder}</span>;
    const opt = options.find((o) => o.value === selectedValue);
    return opt?.label ?? placeholder;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setHighlightedIndex((prev) => Math.min(prev + 1, filteredOptions.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setHighlightedIndex((prev) => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      const item = filteredOptions[highlightedIndex];
      if (item && !item.disabled) handleSelect(item.value);
    } else if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    setOptions(initialOptions);
  }, [initialOptions]);

  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setHighlightedIndex(-1);
    }
  }, [isOpen]);
  /* -------------------- dropdown -------------------- */
  const dropdown = (
    <div
      className={cn(
        'bg-white-50 text-black-B300 absolute z-50 mt-2 max-h-[320px] w-full overflow-hidden rounded-[16px] shadow-[0px_0px_20px_-2px_rgba(16,24,40,0.3)]',
        classNameDropdown
      )}>
      {searchable && (
        <div className="p-3">
          <div className="border-input-border-300 bg-input-bg-300 flex items-center gap-2 rounded-[56px] border px-3 py-3">
            <input
              type="text"
              placeholder={searchPlaceholder}
              disabled={isDisabled || loading}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              className="text-black-300 placeholder-black-100 flex-1 bg-transparent text-sm focus:outline-none"
            />
          </div>
        </div>
      )}

      <ul ref={listRef} className="max-h-[260px] space-y-0.5 overflow-y-auto px-2 pb-4">
        {loading ? (
          <li className="py-2 text-center text-sm text-gray-400">{t('loading')}...</li>
        ) : filteredOptions.length ? (
          filteredOptions.map((option, index) => {
            const active = highlightedIndex === index;
            const selected = option.value === selectedValue;
            return (
              <li
                key={option.value}
                onClick={() => !option.disabled && handleSelect(option.value)}
                className={cn(
                  'hover:bg-white-200 inline-flex w-full cursor-pointer items-center gap-2 px-3.5 py-2',
                  selected && 'bg-white-200 text-primary-300',
                  active && !selected && 'bg-white-200',
                  option.disabled && 'text-primary-300 cursor-not-allowed opacity-50'
                )}>
                <span className="flex-1 text-base font-normal leading-normal">{option.label}</span>
              </li>
            );
          })
        ) : (
          <li className="py-2 text-center text-sm text-gray-400">{t('no_results')}</li>
        )}
      </ul>
    </div>
  );

  /* -------------------- trigger -------------------- */
  const trigger = (
    <div
      tabIndex={0}
      onClick={() => !isDisabled && setIsOpen((prev) => !prev)}
      onKeyDown={handleKeyDown}
      className={cn(
        'md-text flex h-12 w-full items-center justify-between rounded-md border px-3 py-2 text-sm',
        'bg-gray-250 border-gray-150 hover:border-primary-200 flex-row',
        'focus-visible:border-primary-200 focus-visible:outline-none focus-visible:ring-2',
        isDisabled && 'cursor-not-allowed bg-gray-50 text-gray-100'
      )}>
      <span className="truncate">{renderSelected()}</span>
      <div
        className={cn(
          'border-secondary flex h-4 w-4 items-center justify-center rounded-full border',
          isOpen && 'rotate-180',
          isDisabled && 'cursor-not-allowed text-gray-100 opacity-50'
        )}>
        <ChevronDown className="h-3 w-3" />
      </div>
    </div>
  );

  /* -------------------- render -------------------- */
  return (
    <div className={cn('relative w-full', className)} ref={containerRef} dir={direction}>
      {trigger}
      {isOpen && dropdown}
    </div>
  );
}
