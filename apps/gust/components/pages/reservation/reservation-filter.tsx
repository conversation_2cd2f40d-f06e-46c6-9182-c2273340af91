'use client';
import { useEffect, useState, type FC, type ReactElement } from 'react';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn, parseDateToString } from '@/lib/utils';
import { reservationFiltering } from '@/types/reservation';
import PropertySearchField from '@/components/homepage/propery-filter/property-search-field';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import SearchTriggerButton from '@/components/header/search-bar/search-trigger-button';
import IconWrapper from '@/components/icons';
import { Calendar as UiCalendar } from '@/components/ui/calendar';
import { DateRange } from 'react-day-picker';
import PropertySearchToggle from '@/components/homepage/propery-filter/property-search-toggle';
import { useGetReservationStatus } from '@/hooks/use-reservation-static-data';
import { FilterRequest } from '@/types/search';
import SelectWithSearch from '@/components/common/SelectWithSearch';

type ReservationFilterProps = {
  reservationFilter: reservationFiltering[] | undefined;
  query: FilterRequest;
  date: DateRange | undefined;
  setDate: (value: DateRange | undefined) => void;
  setNewQuery: (value: string | number | string[], key: string, onSuccess?: (data: FilterRequest) => void) => void;
};

const ReservationFilter: FC<ReservationFilterProps> = ({
  setDate,
  reservationFilter,
  date,
  query,
  setNewQuery,
}): ReactElement => {
  const frt = useScopedI18n('filter_reservation');
  const status = useGetReservationStatus(query.type || '');
  const [dateSelected, setDateSelected] = useState<boolean>(false);
  const locale = useCurrentLocale();

  const renderCalender = (): ReactElement => {
    return (
      <UiCalendar
        defaultMonth={date?.from}
        mode="range"
        numberOfMonths={2}
        onSelect={setDate}
        selected={date}
        className="mobile:w-auto w-[88vw]"
      />
    );
  };

  const filterClassificationData = reservationFilter?.find((item) => `${item.id}` === `${query.main_id}`);
  const isHaveClassification = filterClassificationData?.classification?.[0]?.classification_name;
  const filterUnitData = filterClassificationData?.classification?.find(
    (classification) => String(classification.id) === String(query.room_classification_id)
  )?.units;

  useEffect(() => {
    if (date?.from) {
      setNewQuery(parseDateToString(date?.from), 'checkin');
    }
    if (date?.to) {
      setNewQuery(parseDateToString(date?.to), 'checkout');
    }
  }, [date]);

  return (
    <div className="xs:grid-cols-1 scrollable-content grid max-h-[53vh] min-h-[100px] gap-1 overflow-auto overflow-x-hidden px-5">
      <PropertySearchField className="py-1" title={frt('propertyName')}>
        <SelectWithSearch
          options={
            reservationFilter?.map((item) => {
              return {
                label: item.name,
                value: String(item.id),
              };
            }) ?? []
          }
          classNameDropdown="relative"
          value={query.main_id}
          onChange={(value) => {
            setNewQuery(value, 'main_id');

            const selectedProperty = reservationFilter?.find((item) => String(item.id) === value);
            if (selectedProperty?.type === 'identical_units') {
              const firstClassification = selectedProperty?.classification?.[0]?.id;
              if (firstClassification) {
                setNewQuery(String(firstClassification), 'room_classification_id');
              }
            } else if (selectedProperty?.type === 'one_unit') {
              setNewQuery('', 'room_classification_id');
            }
          }}
          searchPlaceholder={frt('search_property_name_placeholder')}
          placeholder=""
        />
      </PropertySearchField>

      {Boolean(isHaveClassification) && (
        <PropertySearchField title={frt('propertyClassifications')}>
          <PropertySearchToggle
            type="single"
            items={filterClassificationData?.classification ?? []}
            value={query.room_classification_id}
            setValue={(value) => {
              setNewQuery(value, 'room_classification_id');
            }}
          />
        </PropertySearchField>
      )}
      {filterClassificationData?.type !== 'one_unit' && Boolean(filterUnitData?.length) && (
        <PropertySearchField separator title={frt('propertyUnits')}>
          <PropertySearchToggle
            type="single"
            items={filterUnitData ?? []}
            value={query.unit_id}
            setValue={(value) => {
              setNewQuery(value, 'unit_id');
            }}
          />
        </PropertySearchField>
      )}
      <PropertySearchField className="py-1" title={frt('reservationStatus')}>
        <Select
          value={query.status}
          onValueChange={(value) => {
            setNewQuery(value, 'status');
          }}>
          <SelectTrigger className={cn('w-full ', locale === 'ar' ? 'text-right' : 'text-left')}>
            <SelectValue placeholder={''} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {status?.map((item) => {
                return (
                  <SelectItem key={item.id} value={String(item.value)}>
                    {item.label}
                  </SelectItem>
                );
              })}
            </SelectGroup>
          </SelectContent>
        </Select>
      </PropertySearchField>
      <PropertySearchField className="py-1" title={frt('reservationDate')}>
        <Popover
          open={dateSelected}
          onOpenChange={(open) => {
            setDateSelected(open);
          }}>
          <PopoverTrigger
            className={cn(
              'xs:flex-row  xs:items-center  flex flex-col gap-2',
              'mobile:rounded-full w-full rounded-[14px] bg-transparent py-3 hover:bg-transparent',
              dateSelected && ' bg-white-50',
              locale === 'ar' ? 'pl-4' : 'pr-4'
            )}>
            <div className="xs:flex-row xs:items-center flex w-full flex-1 flex-col items-start gap-2">
              <p className="text-secondary text-md font-semibold">{frt('from')}</p>
              <SearchTriggerButton
                className={cn(
                  'mobile:w-[8.9rem] bg-input-bg-300 border-input-border-300 relative h-[48px] w-full flex-row justify-start rounded-lg border',
                  locale === 'ar' ? 'pr-3' : 'pl-3'
                )}
                classNameIcon={cn(
                  'p-0 bg-secondary-50 w-[3rem] h-[2.9rem] absolute flex justify-center items-center rounded-r-lg',
                  locale === 'ar' ? 'left-0  rotate-180' : 'right-0'
                )}
                bottomParagraph={date?.from === undefined ? '' : date.from.toLocaleDateString(locale)}
                icon={<IconWrapper className="text-secondary" name="Calendar" size={24} variant="Linear" />}
                topParagraph={''}
              />
            </div>
            <div className="xs:flex-row xs:items-center flex w-full flex-1 flex-col items-start gap-2">
              <p className="text-secondary text-md font-semibold">{frt('to')}</p>
              <SearchTriggerButton
                className={cn(
                  'mobile:w-[8.9rem] bg-input-bg-300 border-input-border-300 relative h-[48px] w-full flex-row justify-start rounded-lg border',
                  locale === 'ar' ? 'pr-3' : 'pl-3'
                )}
                classNameIcon={cn(
                  'p-0 bg-secondary-50 w-[3rem] h-[2.9rem] absolute flex justify-center items-center rounded-r-lg',
                  locale === 'ar' ? 'left-0  rotate-180' : 'right-0'
                )}
                bottomParagraph={date?.to === undefined ? '' : date.to.toLocaleDateString(locale)}
                icon={<IconWrapper className="text-secondary" name="Calendar" size={24} variant="Linear" />}
                topParagraph={''}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent align="center" className="shadow-3xl w-full rounded-lg" alignOffset={8} sideOffset={12}>
            {renderCalender()}
          </PopoverContent>
        </Popover>
      </PropertySearchField>
    </div>
  );
};

export default ReservationFilter;
