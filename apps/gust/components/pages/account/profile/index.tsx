import { Card } from '@/components/ui/card';
import VerifiedProfileContent from './profile-form';
import { useGetProfileDetails } from '@/queries/host-pages/my-account';
import SkeletonLoader from './skeleton-loader';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { keys } from '@/lib/react-query/keys';

// Validation schema for Profile form

const Profile = () => {
  const { refetch, data, isFetching } = useGetProfileDetails();
  const queryClient = useQueryClient();

  const profileDetails = data?.data?.data || {};

  const refetchProfile = () => {
    refetch();
  };

  useEffect(() => {
    return () => {
      queryClient.invalidateQueries({ queryKey: keys.profileDetails });
    };
  }, []);

  if (isFetching) return <SkeletonLoader />;

  return (
    <Card className="py-0">
      <div className="py-4">
        <VerifiedProfileContent {...{ profileDetails, refetchProfile }} />
      </div>
    </Card>
  );
};

export default Profile;
