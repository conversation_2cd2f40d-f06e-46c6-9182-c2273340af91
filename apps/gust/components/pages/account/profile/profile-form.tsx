import { useScopedI18n } from '@/lib/i18n/client-translator';
import InputTel from '@/components/ui/input-tel';
import { useCallback, useState } from 'react';
import { Input } from '@/components/hostPages/common';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import UploadAvatar from './upload-avatar';
import { Button } from '@/components/ui/button';
import { Gender } from '../components/rhf-select';
import PhonelEdit from '@/components/profile/phone/phone-edit';
import EmailEdit from '@/components/profile/email/email-edit';
import { useEditProfile } from '@/queries/account';
import { useEffect } from 'react';
import { toast } from 'sonner';
import { getPhoneInputOptions } from '@/components/profile/helpers';
import useWindowSizeQuery from '@/hooks/use-window-size-query';
import { useQueryClient } from '@tanstack/react-query';
import { keys } from '@/lib/react-query/keys';
import { useUserStore } from '@/store/useUserStore';
import { DatePicker } from '../components/rhf-date-picker';
import { parseDateToString } from '@/lib/utils';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const VerifiedProfileContent = ({ profileDetails, refetchProfile }) => {
  const th = useScopedI18n('hostPages.accountPage');
  const t = useScopedI18n('authentication');
  const tValidation = useScopedI18n('validation');

  const isMobile = useWindowSizeQuery('mobile');

  const [editEmail, setEditEmail] = useState(false);
  const [editPhone, setEditPhone] = useState(false);

  const { setUserData, setProfilePicture } = useUserStore();
  const editProfile = useEditProfile();
  const queryClient = useQueryClient();
  const phoneInputOpt = getPhoneInputOptions();

  const methods = useForm({
    resolver: zodResolver(
      z.object({
        first_name: z
          .string({
            required_error: tValidation('required', { field: t('first_name') }),
          })
          .min(2, tValidation('authentication.name_min', { field: t('first_name') }))
          .max(20, tValidation('authentication.name_max', { field: t('first_name') }))
          .regex(
            new RegExp('^[a-zA-Zءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$'),
            tValidation('authentication.only_letters')
          ),
        last_name: z
          .string({
            required_error: tValidation('required', { field: t('last_name') }),
          })
          .min(2, tValidation('authentication.name_min', { field: t('last_name') }))
          .max(20, tValidation('authentication.name_max', { field: t('last_name') }))
          .regex(
            new RegExp('^[a-zA-Zءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$'),
            tValidation('authentication.only_letters')
          ),
        image: z.any().optional(),
        phone_number: z.any().optional(),
        phone_code: z.any().optional(),
        email: z.any().optional(),
        gender: z.any().optional(),
        dob: z.any().optional(),
      })
    ),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      gender: '',
      dob: '',
      phone_number: '',
      image: '',
    },
    mode: 'onChange',
  });

  const {
    register,
    control,
    setValue,
    reset,
    watch,
    handleSubmit,
    formState: { isDirty, dirtyFields, errors },
  } = methods;

  const handleDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];

      const newFile: any = Object.assign(file, {
        preview: URL.createObjectURL(file),
      });

      if (file) {
        setValue('image', newFile, { shouldValidate: true, shouldDirty: true });
      }
    },
    [setValue]
  );

  const onSaveDataChanges = (formValues) => {
    formValues.dob = parseDateToString(formValues.dob as Date);
    // Prepare FormData object
    const formData = new FormData();
    Object.keys(dirtyFields).forEach((field) => {
      formData.append(field, formValues[field]);
    });

    editProfile.mutate(formData, {
      onSuccess: async (response) => {
        const successMsg = response?.data?.message;
        if (successMsg) {
          toast.success(successMsg);
        }
        await queryClient.invalidateQueries({ queryKey: keys.userProfile });
        reset({
          first_name: response.data.data.first_name,
          last_name: response.data.data.last_name,
          email: response.data.data.email_id,
          gender: response.data.data.gender,
          dob: response.data.data.dob,
          phone_number: response.data.data.phone_number_full,
          image: response.data.data.profile_picture,
        });
        if (response) {
          setProfilePicture(response.data.data?.profile_picture);
          setUserData(response.data.data);
        }
      },

      onError: (error: any) => {
        console.error('Error saving profile:', error);
        toast.error(error.response?.data?.message);
      },
    });
  };

  useEffect(() => {
    if (profileDetails) {
      reset({
        first_name: profileDetails.first_name,
        last_name: profileDetails.last_name,
        email: profileDetails.email_id,
        gender: profileDetails.gender,
        dob: profileDetails.dob,
        phone_number: profileDetails.phone_number_full,
        image: profileDetails.profile_picture,
      });
    }
  }, [reset, profileDetails]);

  return (
    <div className="flex flex-col ">
      <h2 className="text-secondary-300 border-gray/20 border-b px-6 py-4 text-2xl font-bold">{th('profile')}</h2>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSaveDataChanges)}>
          <div className="mobile:p-6 p-4">
            <div className="mb-6 flex items-end gap-3">
              <Controller
                name="image"
                control={control}
                render={({ field }) => (
                  <UploadAvatar
                    className="border-white-full drop-shadow-custom bg-gray/5 mb-4 rounded-full border-[2px]"
                    file={
                      typeof field.value === 'string'
                        ? field.value
                        : (field.value as File & { preview: string })?.preview
                    }
                    onDrop={handleDrop}
                    {...field}
                  />
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 ">
              <Input label={t('first_name')} errMsg={errors.first_name?.message} {...register('first_name')} />
              <Input label={t('last_name')} errMsg={errors.last_name?.message} {...register('last_name')} />
              <div>
                <div className="text-secondary-300 text-md mb-3 font-bold">
                  <span className="flex cursor-pointer items-center justify-between">
                    {t('phone_number')}
                    <span className="text-primary-300 font-normal" onClick={() => setEditPhone(true)}>
                      {th('edit')}
                    </span>
                  </span>
                </div>

                <InputTel
                  inputProps={{ disabled: true }}
                  initialValue={'+' + profileDetails?.phone_code + profileDetails?.phone_number}
                  className="hover:border-gray-150 !opacity-100"
                  initOptions={phoneInputOpt}
                />
              </div>

              <Input
                disabled={!!profileDetails?.email_id}
                label={
                  <span
                    className="flex  cursor-pointer items-center justify-between"
                    onClick={() => setEditEmail(true)}>
                    {t('email')} <span className="text-primary-300 font-normal">{th('edit')}</span>
                  </span>
                }
                {...register('email')}
              />
              <DatePicker value={watch('dob')} name={'dob'} placeholder={'dob'} />

              <Gender gender={watch('gender')} />
            </div>
          </div>

          <div className="border-gray/20 border-t p-4">
            <Button
              size="lg"
              type="submit"
              disabled={!isDirty || editProfile.isPending}
              className="mobile:max-w-max w-full shadow-none"
              loading={editProfile.isPending}>
              {th('save_data')}
            </Button>
          </div>
        </form>
      </FormProvider>

      {editEmail && (
        <EmailEdit
          isBottomSheet={isMobile}
          refetchProfile={refetchProfile}
          oldEmail={profileDetails?.email_id}
          editEmailOpen={editEmail}
          setEditEmailOpen={setEditEmail}
          oldPhone={profileDetails?.phone_number}
          emailTime={profileDetails?.email_time}
        />
      )}
      {editPhone && (
        <PhonelEdit
          isBottomSheet={isMobile}
          refetchProfile={refetchProfile}
          editPhoneOpen={editPhone}
          setEditPhoneOpen={setEditPhone}
          oldPhone={profileDetails?.phone_number}
          phoneTime={profileDetails?.phone_time}
        />
      )}
    </div>
  );
};

export default VerifiedProfileContent;
