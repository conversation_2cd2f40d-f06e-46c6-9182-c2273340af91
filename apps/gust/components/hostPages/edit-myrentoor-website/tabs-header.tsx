import TabListTriggers from '@/components/common/tab-list-triggers';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { TabsType } from '@/types/homepage';
import { useEffect } from 'react';
import { useChangeQuery } from '../my-rentoor-websits/utils';

const TabsHeader = () => {
  const t = useScopedI18n('hostPages.myPropertiesPage');
  const { onChangeQuery, paramsWithValues } = useChangeQuery();
  const currentActiveTab = paramsWithValues.activeTab;

  const tabsData = [
    { title: 'nameSettings', trans: t('settings_website_name') },
    { title: 'themeSettings', trans: t('themes') },
    { title: 'contactInfoSettings', trans: t('contact_info_settings') },
    { title: 'emailsSetting', trans: t('emails_setting') },
  ];

  const onChangeActiveTab = (activeTab) => {
    onChangeQuery({
      activeTab,
      page: '1',
    });
  };

  useEffect(() => {
    if (!currentActiveTab) onChangeQuery({ activeTab: 'nameSettings' });
  }, []);

  return (
    <div className="mb-3 flex w-full items-center justify-between">
      <TabListTriggers
        items={tabsData as TabsType[]}
        className="max-h-full w-full px-6 py-[15px]"
        containerClassName="h-[46px] mb-0 w-full"
        containerStyle={{ boxShadow: '0px 2px 12px 0px #0000001F inset', border: '0.2px solid #D8D8D8' }}
        handleSelectType={onChangeActiveTab}
      />
    </div>
  );
};

export default TabsHeader;
