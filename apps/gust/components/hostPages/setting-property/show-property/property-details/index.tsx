'use client';
import { type FC, type ReactElement } from 'react';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import GalleryThree from '@/components/common/gallery';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useGetAmenitiesTabs, useGetPropertyDetailTabs } from '@/hooks/use-get-amenities-tabs';
import TabListTriggers from '@/components/common/tab-list-triggers';
import PropertyDetailsActions from './property-details-actions';
import PropertyDetailsSummary from './property-details-summary';
import PropertyDetailsTab from './property-details-tab';
import Location from './location-details';
import PropertyDetailsPicturesTab from './property-details-pictures-tab';
import PropertyDetailsRatingsTab from './property-details-ratings-tab';
import Carousel from '@/components/ui/carousel';
import PropertySkeletonDetails from './property-skeleton-details';
import Link from 'next/link';
import { useGetPropertyHostDetails } from '@/queries/host-pages/my-properties';

type PropertyDetailsProps = {
  propertyId: string;
  roomClassificationId: string;
};

const PropertyDetails: FC<PropertyDetailsProps> = ({ propertyId, roomClassificationId }): ReactElement => {
  const hlt = useScopedI18n('homepage.location');
  const { data, isPending } = useGetPropertyHostDetails(propertyId, roomClassificationId);
  const property = data?.data.data;

  const amenities = useGetAmenitiesTabs(property);
  const propertyTabs = useGetPropertyDetailTabs();

  if (isPending || property === undefined) {
    return <PropertySkeletonDetails />;
  }

  return (
    <div className="mt-16 flex flex-col gap-y-8">
      <div className="flex flex-col gap-y-6">
        <div className="flex flex-col gap-5">
          <div className=" flex w-full items-center justify-between px-6 md:relative md:top-0 md:z-0 md:px-0">
            <p className="text-secondary headline-4 hidden md:block">{property.classification_name || property.name}</p>
            <PropertyDetailsActions propertyId={propertyId} roomClassificationId={roomClassificationId} />
          </div>
          <div className="relative md:hidden">
            <Carousel images={property.photos} />
          </div>
        </div>
        <div className="hidden items-center justify-between md:flex">
          <PropertyDetailsSummary item={property} />
        </div>
        <div className="hidden md:block">
          <GalleryThree images={property.photos} />
        </div>
        <div className="mt-5 md:hidden" />
        <div className="bg-white-50 z-20 -mt-12 mb-28 rounded-t-2xl px-6 md:mt-0 md:rounded-t-none md:bg-transparent md:px-0">
          <div className="flex flex-col gap-14 md:flex-row">
            <div className="mt-4 flex w-full flex-col gap-6 md:mt-0">
              <Tabs defaultValue="detail">
                <TabListTriggers
                  items={propertyTabs}
                  className="basis-1/4 hover:basis-1/3 data-[state=active]:basis-1/3"
                />
                <TabsContent className="bg-transparent" value="detail">
                  <PropertyDetailsTab property={property} amenities={amenities} />
                </TabsContent>
                <TabsContent className="bg-transparent" value="location">
                  <div className="relative grid h-[43vh] w-full  xl:grid-cols-1 ">
                    <Location properties={property} />
                  </div>
                  <div className="mt-5 flex w-full items-center justify-between">
                    <div className="flex flex-col gap-2">
                      <p className="lg-text text-secondary font-bold">{`${hlt('location')} ${property?.address?.city},${property.t_direction}`}</p>
                      <p className="lg-text text-gray-400">{hlt('title')}</p>
                    </div>

                    <Link
                      href={`https://www.google.com/maps?q=${property.address.city_latLong.latitude},${property.address.city_latLong.longitude}`}
                      target="_blank">
                      <Button
                        variant="outline-secondary"
                        className="text-secondary sm-text mt-2 h-auto gap-2 rounded-full border-none p-4 hover:bg-transparent hover:shadow-none lg:mt-0">
                        <p className="lg-text text-gray-400">{hlt('link')}</p>
                        <IconWrapper name="Routing" size={30} className="bg-primary rounded p-1 text-white" />
                      </Button>
                    </Link>
                  </div>
                </TabsContent>
                <TabsContent className="bg-transparent" value="pictures">
                  <PropertyDetailsPicturesTab {...{ propertyId, roomClassificationId }} />
                </TabsContent>
                <TabsContent className="bg-transparent" value="reviews">
                  <PropertyDetailsRatingsTab propertyId={propertyId} roomClassificationId={roomClassificationId} />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PropertyDetails;
