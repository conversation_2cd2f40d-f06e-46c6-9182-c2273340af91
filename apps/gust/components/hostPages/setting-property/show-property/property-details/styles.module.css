.wrapper > div {
  @apply grid auto-rows-[250px] grid-cols-2 gap-2 md:gap-4;
}
 
.navBtn path {
  @apply stroke-secondary md:stroke-gray-500;
}

.carouselWrapper {
  padding-bottom: 60px;
}
.carouselWrapper :global(.splide__pagination) {
  bottom: 35px;
  justify-content: flex-start;
}
.likeBtn path {
  @apply stroke-primary;
}
.likeBtn svg {
  @apply fill-primary;
}

.bookBtn {
  @apply h-16 grow  rounded-none;
  @apply rounded-se-6xl md:rounded-es-lg;
}
