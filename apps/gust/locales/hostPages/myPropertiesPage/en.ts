export default {
  active: 'Active',
  pending: 'Pending',
  preparing: 'Preparing',
  create_new_website: 'Create New Website',
  my_property_listings: 'My Property Listings',
  link_website: 'Website Link',
  draft: 'Draft',
  sort: 'Sort',
  add_new_property: 'Add new property',
  sorry_there_are_no_properties_currently_associated_with_your_account_you_can_add_a_new_property_to_get_started_or_explore_our_options:
    'Sorry, there are no properties currently associated with your account. You can add a new property to get started or explore our options.',
  property: 'Property',
  location: 'location',
  last_update: 'Last Update',
  actions: 'Actions',
  view: 'View',
  edit: 'Edit',
  management: 'Management',
  copy: 'Copy',
  property_status: 'Property Status',
  resubmit: 'Resubmit',
  under_review: 'Under Review',
  suspended: 'Suspended',
  unlisted: 'Unlisted',
  listed: 'Listed',
  rejection_reasons: 'Rejection Reasons',
  property_details: 'Property Details',
  draft_type: 'Draft Type',
  draft_no: 'Draft No.',
  delete: 'Delete',
  recovery: 'Recovery',
  permanently_delete: 'Permanently Delete',
  classifications: 'Classifications',
  classification: 'Classification',
  units_count: 'Units count',
  bed_room_count: 'Bed room count',
  unit_area: 'Unit area',
  unit_size: 'm²',
  add_new_classification: 'Add a new classification',
  back_to_my_properties: 'Back to My Properties',
  my_properties: 'My Properties',
  rejection_reasons_with_user_name:
    'The property listing was rejected by {user_name} for the following reasons, please edit the property and upload it for approval again. Reasons:',
  edit_property: 'Edit Property',
  admin_rejection_reasons_message:
    'The property listing was rejected by the platform for the following reasons, please modify the property and upload it for approval again. Reasons:',
  status_details: 'Status details',
  the_property_is_under_review_by_the_platform: 'The property is under review by the platform',
  the_classification_is_under_review_by_the_platform: 'The classification is under review by the platform',
  the_property_is_under_review_by_the_host_or_assistant_manager:
    'The property is under review by the host/assistant manager.',
  the_classification_is_under_review_by_the_host_or_assistant_manager:
    'The classification is under review by the host/assistant manager.',
  send_reminder: 'Send reminder',
  the_property_is_suspended_due_to: 'The property is suspended due to:',
  contact_support: 'Contact Support',
  please_explain_to_the_property_manager_why_you_are_rejecting_the_property_addition_and_what_changes_you_would_like:
    'Please explain to the property manager why you are rejecting the property addition and what changes you would like.',
  reason_for_rejection_and_required_modifications: 'Reason for rejection and required modifications',
  confirm: 'Confirm',
  cancel: 'Cancel',
  reject_adding_property: 'Reject adding property',
  delete_classification: 'Delete classification',
  please_select_the_reason_why_you_want_to_delete_classification:
    'Please select the reason why you want to delete classification',
  delete_property: 'Delete property',
  please_select_the_reason_why_you_want_to_delete_property: 'Please select the reason why you want to delete property',
  please_explain_the_reason_for_deletion: 'Please explain the reason for deletion',
  other: 'Other',
  are_you_sure_you_want_to_delete_this_property_if_you_continue_the_category_will_be_deleted_initially:
    'Are you sure you want to delete this property? If you proceed, the property will be permanently deleted, and you will lose all reviews, which cannot be recovered.',

  are_you_sure_you_want_to_delete_this_category_if_you_continue_the_category_will_be_deleted_initially:
    'Are you sure you want to delete this category? If you continue, the category will be deleted initially.',
  continue: 'Continue',
  confirm_delete_classification: 'Confirm delete classification',
  confirm_delete_property: 'Confirm delete Property',
  confirmation_of_property_return: 'Confirmation of property return',
  do_you_want_to_restore_the_property_the_property_will_be_restored_to_its_previous_condition_upon_confirmation:
    'Do you want to restore the property? The property will be restored to its previous condition upon confirmation',
  confirmation_of_classification_return: 'Confirmation of classification return',
  do_you_want_to_restore_the_classification_the_classification_will_be_restored_to_its_previous_condition_upon_confirmation:
    'Do you want to restore the classification? The classification will be restored to its previous condition upon confirmation',
  please_select_a_reason: 'Please, select a reason',
  please_add_your_reason: 'please, add your reason',
  latest: 'Latest',
  oldest: 'Oldest',
  sort_my_properties_by: 'Sort my properties by',
  save: 'save',
  sort_my_websites_by: 'Sort my websites by',
  location_not_set_yet: 'Location not set yet',
  search_by_guest_name_and_property_name: 'Search by property name',

  propType: {
    one_unit: 'One-unit property',
    identical_units: 'Property of several units',
    different_classifications: 'Property of different classifications',
  },
  noActiveProperties: 'There are no active properties at the moment.',
  noPendingProperties: 'There are no pending properties at the moment.',
  noDraftProperties: 'There are no draft properties at the moment.',
  settings_website_name: 'Settings Website Name',
  themes: 'Themes',
  contact_info_settings: 'Contact Info Settings',
  emails_setting: 'Emails Setting',
};
