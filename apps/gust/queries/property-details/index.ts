import { type UseQueryResult, useQuery, type UseMutationResult, useMutation } from '@tanstack/react-query';
import type { AxiosResponse } from 'axios';
import { API } from '@/lib/axios/axios';
import { ClassificationDetails, Photos, type Classification } from '@/types/homepage';
import { getDynamicKey, keys } from '@/lib/react-query/keys';
import type { GetResponse } from '@/types/common';
// import { useCurrentLocale } from '@/lib/i18n/client-translator';
import {
  type ImageWithDimensions,
  type PropertyReservationPriceRequest,
  type ReservationDetails,
} from '@/types/room-details';
// import { getAccessToken } from '@/queries/authentication/access-token';

export const getPropertyDetails = async (
  classId,
  pageName?: string,
  isHost?: boolean
): Promise<AxiosResponse<GetResponse<Classification | any>>> => {
  let queryPath =
    pageName === 'reservation' ? `v2/reservation/property/details/${classId}` : `/v2/properties/${classId}`;
  if (isHost) {
    queryPath = `v3/host/property/steps/${classId}`;
  }
  return API().get<GetResponse<Classification | any>>(queryPath);
};

export const useGetPropertyDetails = (
  propertyId: string,
  pageName?: string,
  refetchOnWindowFocus?: boolean
): UseQueryResult<AxiosResponse<GetResponse<any> | any>> => {
  return useQuery({
    queryKey: getDynamicKey('propertyDetails', [propertyId]),

    queryFn: () => getPropertyDetails(propertyId, pageName),
    enabled: !!propertyId,
    retryOnMount: !!pageName,
    refetchOnMount: 'always',
    refetchOnWindowFocus: refetchOnWindowFocus,
  });
};

export const useGetPropertyReservationPrice = (
  classId
): UseMutationResult<AxiosResponse<GetResponse<ReservationDetails>>> => {
  return useMutation({
    mutationFn: async (data: PropertyReservationPriceRequest) => {
      return API().post(`/v2/properties/${classId}/price`, data);
    },
  });
};

export const useReservationInvoiceDetailsPrice = (): UseMutationResult<
  AxiosResponse<GetResponse<ReservationDetails>>
> => {
  return useMutation({
    mutationFn: async (data: PropertyReservationPriceRequest) => {
      return API().post(`/v2/reservation/invoice`, data);
    },
  });
};

export const getCalenderAvailable = async (
  propertyId: string,
  year: string,
  month: string
): Promise<AxiosResponse<GetResponse<any>>> => {
  const queryPath = `/v2/properties/${propertyId}/calender?year=${year}&month=${month}`;
  return API().get<GetResponse<any>>(queryPath);
};

export const useGetCalenderAvailable = (
  propertyId: string,
  year: string,
  month: string
): UseQueryResult<AxiosResponse<GetResponse<any>> | null> => {
  return useQuery({
    queryKey: getDynamicKey('properties', [propertyId, year, month]),
    queryFn: () => getCalenderAvailable(propertyId, year, month),
    enabled: !!propertyId,
  });
};

export const useGetImageDimensions = (): UseMutationResult<ImageWithDimensions[]> => {
  return useMutation({
    mutationFn: async (data: { images: Photos[] }) => {
      const tmp: ImageWithDimensions[] = [];
      for (const img of data.images) {
        const dm = await getImageDimensions(img.original_image);
        tmp.push(dm);
      }
      return tmp;
    },
  });
};

export const getImageDimensions = (url: string): Promise<ImageWithDimensions> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
        src: url,
      });
    };
    img.onerror = (error) => {
      reject(error);
    };
    img.src = url;
  });
};
export const getPropertyHostDetails = async (
  propertyId,
  roomClassificationId
): Promise<AxiosResponse<GetResponse<Classification>>> => {
  const queryPath = `/v2/host/properties/${propertyId}/classifications/${roomClassificationId}`;
  return API().get<GetResponse<Classification>>(queryPath);
};

export const getMyPropertiesClassificationDetails = async ({
  page,
  type,
  pageSize,
  classificationId,
}: {
  page?: number;
  type: string;
  pageSize?: number;
  classificationId: string | number;
}): Promise<AxiosResponse<GetResponse<any>>> => {
  const queryPath = `/v2/host/properties/${classificationId}/classifications`;
  return API().get<GetResponse<any>>(queryPath, {
    params: {
      type,
      page,
      per_page: pageSize,
    },
  });
};

export const useGetPropertyPhotos = ({ classificationId }: { classificationId: string }) => {
  return useQuery({
    queryKey: [keys.getPropertyDetailsPhotos, classificationId],
    queryFn: () => API().get(`/v2/properties/${classificationId}/photos`),
    enabled: !!classificationId,
  });
};
